package com.ybm.app.common.apicache;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.ArrayMap;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.bean.ApiCacheEntity;
import com.ybm.app.bean.SpCacheEntity;
import com.ybm.app.common.BaseYBMApp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * sp实现的数据缓存处理类
 */

public class DataPersistenceBySp extends BaseDataPersistence<SharedPreferences.Editor> {
    private SharedPreferences sp;
    private final static String CACHE_NAME = "ybm_api_cache";

    public DataPersistenceBySp(IDataEncrypt encrypt) {
        super(encrypt);
        getSp();
    }

    @Override
    protected String getCache4Iml(String key) {
        if (key == null) {
            return null;
        }
        return readString(key);
    }

    @Override
    protected boolean putCache4Iml(String key, String value) {
        long strat = System.currentTimeMillis();
        if (value == null) {
            delString(key);
        } else {
            writeString(key, value);
        }
        LogUtils.d("key:" + key + " : " + "存缓存时间：" + (System.currentTimeMillis() - strat));
        return true;
    }

    @Override
    protected SharedPreferences.Editor putCachesStart() {
        return getEdit();
    }

    @Override
    protected void appendCache(String key, String value, SharedPreferences.Editor editor) {
        editor.putString(key, value);
    }

    @Override
    protected void putCachesEnd(SharedPreferences.Editor editor) {
        editor.apply();
    }


    @Override
    public void deleteCache(String key) {
        super.deleteCache(key);
        delString(key);
    }

    @Override
    public void clean() {
        getEdit().clear().apply();
    }

    @Override
    public void checkSize(int maxSize) {
        if (getSp().getAll() == null || getSp().getAll().isEmpty() || getSp().getAll().size() <= maxSize * 1.2) {
//               LogUtils.d(getSp().getAll().size()+" 标准大小："+maxSize*1.2);
            return;
        }
        //已经超过数量
        Map<String, Object> map = (Map<String, Object>) getSp().getAll();
        int mapSize = map.size();
        List<SpCacheEntity> temp = new ArrayList<>(map.size());
        SpCacheEntity entity = null;
        Set<String> keys = map.keySet();
        for (String key : keys) {
            entity = new SpCacheEntity();
            entity.key = key;
            try {
                entity.lastCacheTime = ApiCacheEntity.getLastTime((String) map.get(key));
            } catch (Exception e) {
                entity.lastCacheTime = 0;
            }
            temp.add(entity);
        }
        Collections.sort(temp, new Comparator<SpCacheEntity>() {
            @Override
            public int compare(SpCacheEntity entity1, SpCacheEntity entity2) {
                if (entity2.lastCacheTime - entity1.lastCacheTime > 0) {
                    return -1;
                } else if (entity2.lastCacheTime - entity1.lastCacheTime == 0) {
                    return 0;
                } else {
                    return 1;
                }
            }
        });
        int diff = (int) (mapSize - maxSize + mapSize * 0.3);
        diff = Math.min(diff, temp.size() - 1);
        SharedPreferences.Editor editor = getEdit();
        for (int a = 0; a < diff; a++) {
            editor.remove(temp.get(a).key);
        }
        editor.apply();
//          LogUtils.d("删除时间："+(System.currentTimeMillis()-start) +" 删除数量："+diff +" 删除前数量："+mapSize +" 删除后："+getSp().getAll().size()+" 标准数量："+maxSize);
    }

    private void writeString(String key, String value) {
        getEdit().putString(key, value).apply();
    }

    private void delString(String key) {
        getEdit().remove(key).apply();
    }

    private String readString(String key) {
        return getSp().getString(key, null);
    }

    private SharedPreferences.Editor getEdit() {
        return getSp().edit();
    }

    private SharedPreferences getSp() {
        if (sp == null) {
            sp = BaseYBMApp.getAppContext().getSharedPreferences(CACHE_NAME, Context.MODE_PRIVATE);
        }
        return sp;
    }
}
