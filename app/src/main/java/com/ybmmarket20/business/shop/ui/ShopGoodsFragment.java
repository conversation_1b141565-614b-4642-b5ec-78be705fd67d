package com.ybmmarket20.business.shop.ui;

import static com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt.flowDataPageListPageExposureNew;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.activity.ComponentActivity;
import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.bean.SearchResultBean;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.report.coupon.CouponEntryType;
import com.ybmmarket20.report.coupon.ICouponEntryType;
import com.ybmmarket20.search.SpecFilterPopupWindow;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.AllProductPopWindow;
import com.ybmmarket20.view.BaseFilterPopWindow;
import com.ybmmarket20.view.ListFilterPopWindow;
import com.ybmmarket20.view.Manufacturers2Pop;
import com.ybmmarket20.view.SearchCategorySelectLevelTrack;
import com.ybmmarket20.viewmodel.SearchDataViewModel;
import com.ybmmarketkotlin.adapter.GoodListAdapterNew;
import com.ybmmarketkotlin.utils.ExposureCallback;
import com.ybmmarketkotlin.utils.ExposureManager;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;
import kotlin.Unit;

/**
 * <br>
 * 作者：zhuruqiao
 * 时间：2018/10/13 11:35
 * 邮箱：<EMAIL>
 */
public class ShopGoodsFragment extends BaseFragment implements ICouponEntryType {

    @Bind(R.id.rb_category)
    public RadioButton rbCategory;

    @Bind(R.id.rb_all)
    public RadioButton rbAll;

    @Bind(R.id.rb_sales)
    public RadioButton rbSales;
    @Bind(R.id.rb_manufacturer)
    public RadioButton rbManufacturer;

    @Bind(R.id.rb_comprehensive_ranking)
    public RadioButton rbComprehensiveRanking;

    @Bind(R.id.ll_all)
    LinearLayout brandRg01;

    private RadioButton currentSelect;

    private GoodListAdapterNew goodsAdapter;

    private ListFilterPopWindow mPopWindowRanking;

    @Bind(R.id.rv_goodslist)
    public RecyclerView crv;
    @Bind(R.id.smartrefresh)
    public SmartRefreshLayout smartrefresh;
    @Bind(R.id.rb_spec)
    RadioButton rbSpec;
    @Bind(R.id.rt_ptby)
    TextView rtPtby;
    @Bind(R.id.rt_available_coupon)
    TextView rtAvailableCoupon;

    private boolean isSpecSelected = false;
    private SpecFilterPopupWindow specFilterPopupWindow;
    private String specFilterStr = "";

    private int pageSize = 10;

    private List<RowsBean> goodsList = new ArrayList<>();
    private Manufacturers2Pop mPopManufacturer;
    private List<String> lastNames = new ArrayList<>();
    private String manufacturer;
    private SearchDataViewModel mViewModel;

    public static ShopGoodsFragment getInstance(String orgId, String shopCode, String searchKey, String isThirdCompany, String orgIdShopCode,String entrance) {

        ShopGoodsFragment shopGoodsFragment = new ShopGoodsFragment();
        Bundle bundle = new Bundle();
        bundle.putString("orgId", orgId);
        bundle.putString("shopCode", shopCode);
        bundle.putString("isThirdCompany", isThirdCompany);
        bundle.putString("searchKey", searchKey);
        bundle.putString("orgIdShopCode", orgIdShopCode);
        bundle.putString(IntentCanst.JG_ENTRANCE, entrance);
        shopGoodsFragment.setArguments(bundle);
        return shopGoodsFragment;

    }

    private String orgId;
    private String shopCode;
    private String isThirdCompany;

    private String categoryId;

    private String searchKey;
    private String preSearchKey;
    private String orgIdShopCode;
    private String mEntrance = "";
    //搜索筛选项 是否包含拼团包邮商品 1选中包含  0不包含   空未选中
    private String isGroupBuyingOrWholesale = "";

    //搜索筛选项 是否包含可用劵商品 1选中包含  0不包含   空未选中
    private String isAvailableCoupons = "";

    //根据销量查询
    private boolean sortBySalesCount;

    //排序
    private String property = "smsr.sale_num", propertyDescOrAsc = "desc", propertyName = "";

    @Override
    protected void initData(String content) {

        orgId = getArguments().getString("orgId");
        shopCode = getArguments().getString("shopCode");
        isThirdCompany = getArguments().getString("isThirdCompany");
        searchKey = getArguments().getString("searchKey");
        orgIdShopCode = getArguments().getString("orgIdShopCode");
        mEntrance = getArguments().getString(IntentCanst.JG_ENTRANCE);
        if (mEntrance!=null && !mEntrance.isEmpty()){
            JGTrackTopLevelKt.splicingPageTitle2Entrance(mEntrance, JGTrackManager.TrackShopSearch.TITLE);
        }else {
            mEntrance = JGTrackManager.TrackShopSearch.TITLE;
        }

        mViewModel = new ViewModelProvider((ComponentActivity)getContext()).get(SearchDataViewModel.class);
        initView();

    }

    private void initView() {
        currentSelect = rbAll;
        goodsAdapter = new GoodListAdapterNew(this, R.layout.item_goods_new, goodsList, false);
        JgTrackBean jgTrackBean = new JgTrackBean();
        jgTrackBean.setPageId(JGTrackManager.TrackShopSearch.PAGE_ID);
        jgTrackBean.setTitle(JGTrackManager.TrackShopSearch.TITLE);
        jgTrackBean.setUrl(AppUtilKt.getFullClassName(this));
        jgTrackBean.setModule(JGTrackManager.Common.MODULE_SEARCH_FEED);
        jgTrackBean.setJgReferrer(AppUtilKt.getFullClassName(this));
        jgTrackBean.setJgReferrerTitle(JGTrackManager.TrackShopSearch.TITLE);
        jgTrackBean.setJgReferrerModule(JGTrackManager.TrackShopSearch.TITLE);
        jgTrackBean.setEntrance(mEntrance);
        goodsAdapter.setJgTrackBean(jgTrackBean);
        // 店铺内搜索移除极光埋点 - page_list_product_exposure 事件
        // goodsAdapter.setResourceViewTrackListener((rowsBean, integer,pageListCommonBean) -> {
        //     StringBuilder productTag = new StringBuilder();
        //     String productId = "";
        //     String productName = "";
        //     String productType = "";
        //     double productPrice = 0.0;

        //     if (rowsBean != null) {
        //         if (rowsBean.getProductId() != null) {
        //             productId = rowsBean.getProductId();
        //         }
        //         if (rowsBean.getProductName() != null) {
        //             productName = rowsBean.getProductName();
        //         }
        //         productPrice = rowsBean.getJgProductPrice();
        //     }

        //     if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.productTags != null && rowsBean.tags.productTags.size() > 0) {
        //         for (int i = 0; i < rowsBean.tags.productTags.size(); i++) {
        //             if (i != rowsBean.tags.productTags.size() - 1) {
        //                 productTag.append(rowsBean.tags.productTags.get(i).text).append("，");
        //             } else {
        //                 productTag.append(rowsBean.tags.productTags.get(i).text);
        //             }
        //         }
        //     }
        //     if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.dataTags != null && rowsBean.tags.dataTags.size() > 0) {
        //         if (productTag.length()>0){
        //             productTag.append("，");
        //         }
        //         for (int i = 0; i < rowsBean.tags.dataTags.size(); i++) {
        //             if (i != rowsBean.tags.dataTags.size() - 1) {
        //                 productTag.append(rowsBean.tags.dataTags.get(i).text).append("，");
        //             } else {
        //                 productTag.append(rowsBean.tags.dataTags.get(i).text);
        //             }
        //         }
        //     }

        //     if (rowsBean != null){
        //         productType = rowsBean.getJgProductType();
        //     }

        //     return null;
        // });
        // 店铺内搜索移除极光埋点 - action_list_product_click 和 action_product_button_click 事件
        // goodsAdapter.setProductClickTrackListener((rowsBean, integer,isBtnClick,mContent,number) -> {
        //     StringBuilder productTag = new StringBuilder();
        //     String productId = "";
        //     String productName = "";
        //     double productPrice = 0.0;

        //     if (rowsBean != null) {
        //         if (rowsBean.getProductId() != null) {
        //             productId = rowsBean.getProductId();
        //         }
        //         if (rowsBean.getProductName() != null) {
        //             productName = rowsBean.getProductName();
        //         }
        //         productPrice = rowsBean.getJgProductPrice();
        //     }

        //     if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.productTags != null && rowsBean.tags.productTags.size() > 0) {
        //         for (int i = 0; i < rowsBean.tags.productTags.size(); i++) {
        //             if (i != rowsBean.tags.productTags.size() - 1) {
        //                 productTag.append(rowsBean.tags.productTags.get(i).text).append("，");
        //             } else {
        //                 productTag.append(rowsBean.tags.productTags.get(i).text);
        //             }
        //         }
        //     }

        //     if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.dataTags != null && rowsBean.tags.dataTags.size() > 0) {
        //         if (productTag.length()>0){
        //             productTag.append("，");
        //         }
        //         for (int i = 0; i < rowsBean.tags.dataTags.size(); i++) {
        //             if (i != rowsBean.tags.dataTags.size() - 1) {
        //                 productTag.append(rowsBean.tags.dataTags.get(i).text).append("，");
        //             } else {
        //                 productTag.append(rowsBean.tags.dataTags.get(i).text);
        //             }
        //         }
        //     }
        //     return null;
        // });

//        goodsAdapter.setConfigPreHot(true);
//        goodsAdapter.setConfigSpellGroupSellOut(true);
        goodsAdapter.setEmptyView(getNotNullActivity(), R.layout.layout_empty_view_all_goods, R.drawable.icon_empty, "哇哦，没有找到相关商品");
        goodsAdapter.setOnLoadMoreListener(() -> getLoadMoreResponse());
        goodsAdapter.setShowShopInfo(false);
        crv.setAdapter(goodsAdapter);
        crv.setLayoutManager(new WrapLinearLayoutManager(getContext()));
        // 可见性曝光埋点
        ExposureManager.INSTANCE.registerExposureListener(crv, new ExposureCallback() {
            @Override
            public void callback(@NonNull List<Integer> indexList) {
                StringBuilder indexStr = new StringBuilder();
                for (int index : indexList) {
                    Object itemData = goodsAdapter.getItem(index);
                    BaseFlowData flowData = goodsAdapter.getFlowData();
                    if (flowData != null && itemData instanceof RowsBean) {
                        flowDataPageListPageExposureNew(flowData, ((RowsBean) itemData).getProductId(),
                                ((RowsBean) itemData).getShowName(), index, -1, "");
                        indexStr.append(index).append(" ");
                    }
                }
            }
        });

        smartrefresh.setOnRefreshListener(refreshLayout -> getNewData());
        getNewData();
        showSpecPopupWindow();
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @OnClick({R.id.rb_all, R.id.rb_category, R.id.rb_sales, R.id.rb_comprehensive_ranking, R.id.rb_spec, R.id.rb_manufacturer, R.id.rt_ptby, R.id.rt_available_coupon})
    public void onClick(View view) {

        switch (view.getId()) {

            case R.id.rt_ptby:
                String status;
                rtPtby.setActivated(!rtPtby.isActivated());
                if (rtPtby.isActivated()) {
                    status = "1";
                } else {
                    status = "";
                }
                setPtByState(status);
                getNewData();
                break;

            case R.id.rt_available_coupon:
                String couponStatus = "";
                rtAvailableCoupon.setActivated(!rtAvailableCoupon.isActivated());
                if (rtAvailableCoupon.isActivated()) {
                    couponStatus = "1";
                } else {
                    couponStatus = "";
                }
                setUseJuanStatus(couponStatus);
                getNewData();
                break;

            case R.id.rb_all:
                if (currentSelect != rbAll) {
                    currentSelect.setChecked(false);
                }
                //执行全部商品切换
                sortBySalesCount = false;
                if (mPopWindowProduct != null) {
                    mPopWindowProduct.resetPosition();
                }
                categoryId = "";
                rbCategory.setText("全部分类");
                getNewData();
                break;
            case R.id.rb_category:
                if (currentSelect != rbCategory) {
                    currentSelect.setChecked(false);
                }
                //弹出分类的复选框
                sortBySalesCount = false;
                setIconState(rbCategory, R.drawable.manufacturers_checked_green);
                showCategory();
                break;

            case R.id.rb_spec:
                isSpecSelected = !isSpecSelected;
                rbSpec.setChecked(false);
                setIconState(rbSpec, isSpecSelected ? R.drawable.manufacturers_new_checked : R.drawable.manufacturers_new_def);
                if (isSpecSelected) {
                    //选中
                    showSpecPopupWindow();
                } else {
                    if (specFilterPopupWindow != null) specFilterPopupWindow.dismiss();
                }
                break;

            case R.id.rb_sales:
                if (currentSelect != rbSales) {
                    currentSelect.setChecked(false);
                    //按照销量排序
                    sortBySalesCount = true;
                    getNewData();
                }
                break;
            //综合排序
            case R.id.rb_comprehensive_ranking:
                if (goodsAdapter == null || rbComprehensiveRanking == null) {
                    return;
                }
                if (currentSelect != rbComprehensiveRanking) {
                    currentSelect.setChecked(false);
                }
                ((BaseActivity) getActivity()).hideSoftInput();
                setIconState(rbComprehensiveRanking, R.drawable.sort_checked);
                initPopComprehensiveRanking();
                mPopWindowRanking.show(brandRg01);
                break;

            case R.id.rb_manufacturer:
                initManufacturerPop();
                mPopManufacturer.setDataType(categoryId , searchKey, false, false, false
                        , false, false, false, "", "", lastNames);
                if (orgId == null) {
                    mPopManufacturer.setShopCodes(shopCode);
                } else {
                    mPopManufacturer.setShopCodes(orgIdShopCode);
                }
                mPopManufacturer.show(rbManufacturer);
                break;
        }

        if (view instanceof RadioButton){
            currentSelect = (RadioButton) view;
        }
    }

    /**
     * 设置厂家过来的数据
     *
     * @param bean
     */
    private void setManufacturer(SearchFilterBean bean) {
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        lastNames.addAll(bean.lastNames);
//        if (mClassifyPop != null) {
//            mClassifyPop.setLastNames(lastNames);
//        }
        StringBuilder sb = new StringBuilder();
        if (lastNames != null && lastNames.size() > 0) {
            for (int i = 0; i < lastNames.size(); i++) {
                sb.append(lastNames.get(i)).append("*");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
        }
        manufacturer = sb.toString();
    }

    /**
     * 厂家(非ka)弹出pop
     */
    private void initManufacturerPop() {
        if (mPopManufacturer == null) {
            mPopManufacturer = new Manufacturers2Pop("");
            mPopManufacturer.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(final SearchFilterBean bean) {
                    setManufacturer(bean);
                    getNewData();
                    String trackStr = null;
                    if (bean != null && bean.lastNames != null) {
                        for (String name : bean.lastNames) {
                            if (trackStr == null) {
                                trackStr = name;
                            } else {
                                trackStr = trackStr + "," + name;
                            }
                        }
                    }
                    if (!TextUtils.isEmpty(trackStr)) {
                        HashMap<String, String> trackParams = new HashMap<>();
                        trackParams.put("manufactors", trackStr);
                        XyyIoUtil.track("shop_search_manufactor_filter", trackParams);
                    }
                }

                @Override
                public void OnDismiss(String multiSelectStr) {

                }
            });
        }
    }

    private Map<String, String> getSpecFilterRequestParams() {
//        Map<String, String> map = new HashMap<>();
//        map.put("merchantId", SpUtil.getMerchantid());
//        map.put("keyword", searchKey);
//        map.put("type", "1");
//        map.put("shopCode", shopCode);
//        map.put("orgId", orgId);
//        map.put("categoryId", categoryId);
        Map<String, String> paramsMap = getRequestParams(false, false).getParamsMap();
        paramsMap.remove("spec");
        return paramsMap;
    }

    /**
     * 规格过滤
     */
    private void showSpecPopupWindow() {
        if (specFilterPopupWindow == null && ((searchKey == null && preSearchKey == null) || !searchKey.equals(preSearchKey))) {
            specFilterPopupWindow = new SpecFilterPopupWindow();
            specFilterPopupWindow.loadData(getSpecFilterRequestParams());
            specFilterPopupWindow.setListener(new SpecFilterPopupWindow.SpecFilterCallback() {
                @Override
                public void onGetData(@NotNull String filterStr) {
                    specFilterStr = filterStr;
                    rbSpec.setChecked(false);
                    getNewData();
                    if (mPopWindowProduct != null) {
                        mPopWindowProduct.showShopCategory(orgId);
                    }
                    if (!TextUtils.isEmpty(specFilterStr)) {
                        HashMap<String, String> trackParams = new HashMap<>();
                        trackParams.put("specs", specFilterStr);
                        XyyIoUtil.track("shop_search_spec_filter", trackParams);
                    }
                }

                @Override
                public void onDismiss() {
                    isSpecSelected = false;
                    setIconState(rbSpec, !TextUtils.isEmpty(specFilterStr) ? R.drawable.manufacturers_new_checked : R.drawable.manufacturers_new_def);
                }
            });
        } else {
            specFilterPopupWindow.show(brandRg01);
        }
    }

    /**
     * 设置选中状态
     *
     * @param id
     */
    public void setActivated(int id) {
        if (rbComprehensiveRanking == null) {
            return;
        }
        rbComprehensiveRanking.setActivated(R.id.rb_comprehensive_ranking == id);
    }


    /**
     * 综合排序
     */
    private void initPopComprehensiveRanking() {

        if (mPopWindowRanking == null) {

            mPopWindowRanking = new ListFilterPopWindow();
            mPopWindowRanking.setNewData(mPopWindowRanking.getRanking());
            mPopWindowRanking.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    if (show == null) return;
                    rbComprehensiveRanking.setText(show.nickname);
                    property = show.id;
                    propertyName = show.realName;
                    getNewData();
                    HashMap<String, String> trackParams = new HashMap<>();
                    trackParams.put("item", show.tag);
                    XyyIoUtil.track("shop_search_sort", trackParams);
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    if (TextUtils.isEmpty(property)) {
                        rbComprehensiveRanking.setText("综合排序");
                    }
                    //rbComprehensiveRanking.setActivated(false);
                    setIconState(rbComprehensiveRanking, R.drawable.sort_def);
                }
            });
        }


    }


    @Override
    protected String getUrl() {
        return null;
    }


    @Override
    public int getLayoutId() {
        return R.layout.fragment_shop_goods;
    }

    private void getNewData() {
        if (smartrefresh != null) {
            smartrefresh.postDelayed(this::showProgress, 500);
        }
        // guanchong 店铺详情
        HttpManager.getInstance().post(getRequestParams(true), new BaseResponse<SearchResultBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SearchResultBean> obj, SearchResultBean brandBean) {
                if (getActivity() == null || getActivity().isFinishing()) return;
                smartrefresh.postDelayed(ShopGoodsFragment.this::dismissProgress, 500);
                smartrefresh.finishRefresh();
                if (null != obj) {
                    if (obj.isSuccess() && brandBean != null) {
                        if (brandBean.rows == null) brandBean.rows = new ArrayList<>();
                        updateSearchData(true, brandBean);
                        if (specFilterPopupWindow != null) {
                            specFilterPopupWindow.loadDataOnly(getSpecFilterRequestParams());
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                smartrefresh.finishRefresh();
            }
        });
    }

    private RequestParams searchMoreParams;

    /**
     * 请求搜索后的数据更新
     *
     * @param rowsBeans
     */
    private void updateSearchData(boolean isRefresh, SearchResultBean rowsBeans) {
        searchMoreParams = rowsBeans.getRequestParams();
        if (!TextUtils.isEmpty(rowsBeans.sptype) || !TextUtils.isEmpty(rowsBeans.spid) || !TextUtils.isEmpty(rowsBeans.sid)) {
            FlowDataAnalysisManagerKt.updateFlowData(mFlowData, rowsBeans.sptype, rowsBeans.spid, rowsBeans.sid, rowsBeans.nsid);
            goodsAdapter.setFlowData(mFlowData);
        }
        AdapterUtils.INSTANCE.updateRowsData(rowsBeans.licenseStatus, rowsBeans.rows, goodsAdapter, isRefresh, rowsBeans.isEnd);
    }

    private RequestParams getRequestParams(boolean isRefresh) {
        return getRequestParams(isRefresh, true);
    }

    private RequestParams getRequestParams(boolean isRefresh, boolean isContainUrl) {
        if (isContainUrl) {
            if (!isRefresh) {
                //需要初始化 pageSize 及offset
                if (TextUtils.isEmpty(searchMoreParams.getUrl())) {
                    searchMoreParams.setUrl(SpUtil.isKa() ? AppNetConfig.SORTNET_KA : AppNetConfig.SORTNET);
                }
                return searchMoreParams;
            }
        }

        RequestParams params = new RequestParams();
        if (isContainUrl) {
            params.setUrl(SpUtil.isKa() ? AppNetConfig.SORTNET_KA : AppNetConfig.SORTNET);
        }
        params.put("merchantId", SpUtil.getMerchantid());

        /**
         * 规格
         */
        if (!TextUtils.isEmpty(specFilterStr)) {
            params.putWithoutEncode("spec", specFilterStr);
        }

        // pop店铺
        if (!TextUtils.isEmpty(orgId)) {
            params.put("orgId", orgId);
            params.put("shopCodes", orgIdShopCode);
        }
        // 自营店铺
        if (!TextUtils.isEmpty(shopCode)) {
            params.put("shopCodes", shopCode);
        }
        if (!TextUtils.isEmpty(isThirdCompany)) {
//            params.put("isThirdCompany", isThirdCompany);
        }

        if (!TextUtils.isEmpty(property)) {

            switch (propertyName) {
                //价格从低到高
                case "价格从低到高":
                    propertyDescOrAsc = "asc";
                    break;
                //价格从高到低
                case "价格从高到低":
                default:
                    propertyDescOrAsc = "desc";
            }
            params.put("property", property);
            params.put("direction", propertyDescOrAsc);
        }

        //全部分类 categoryId
        if (!TextUtils.isEmpty(categoryId)) {
            params.put("categoryId", categoryId);
        }

        if (!TextUtils.isEmpty(searchKey)) {
            params.putWithoutEncode("keyword", searchKey);
        }
        // 搜索埋点页面来源：1-大搜；2-店铺内搜索；3-专区搜索；
        params.put("spFrom", "2");
        params.put("isAvailableCoupons", isAvailableCoupons);
        params.put("isGroupBuyingOrWholesale", isGroupBuyingOrWholesale);

        //厂家
        if (!TextUtils.isEmpty(manufacturer)) {
            params.putWithoutEncode("manufacturer", manufacturer);
        }
        FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData);
        return params;
    }

    private void getLoadMoreResponse() {
        HttpManager.getInstance().post(getRequestParams(false), new BaseResponse<SearchResultBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SearchResultBean> obj, SearchResultBean brandBean) {
                if (null != obj) {
                    if (obj.isSuccess() && brandBean != null) {
                        updateSearchData(false, brandBean);
                    }
                }
            }
        });
    }

    private AllProductPopWindow mPopWindowProduct;
    private String categoryName = "";

    private void showCategory() {

        if (mPopWindowProduct == null) {
            mPopWindowProduct = new AllProductPopWindow();
            mPopWindowProduct.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {

                @Override
                public void getValue(SearchFilterBean show) {
                    if (show != null) {
                        categoryId = show.id;
                        categoryName = show.realName;
                    } else {
                        categoryId = "";
                        categoryName = "";
                    }
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    rbCategory.setText(TextUtils.isEmpty(categoryName) ? "全部分类" : categoryName);
                    if ("全部分类".equals(rbCategory.getText())) {
                        rbCategory.setActivated(false);
                    }
                    setIconState(rbCategory, R.drawable.manufacturers_new_def);
                    getNewData();
                }
            });
            mPopWindowProduct.setOnLevelItemClickListener(searchCategorySelectLevelTrack -> {
                if (searchCategorySelectLevelTrack instanceof SearchCategorySelectLevelTrack.FIRST_LEVEL) {
                    //一级
                    searchCategorySelectLevelTrack.track("shop_search_first_classification");
                } else if (searchCategorySelectLevelTrack instanceof SearchCategorySelectLevelTrack.SECOND_LEVEL) {
                    //二级
                    searchCategorySelectLevelTrack.track("shop_search_second_classification");
                } else if(searchCategorySelectLevelTrack instanceof SearchCategorySelectLevelTrack.THIRD_LEVEL) {
                    //三级
                    searchCategorySelectLevelTrack.track("shop_search_third_classification");
                }
            });
        }
        mPopWindowProduct.showShopCategory(brandRg01, orgId);
    }

    private void setIconState(RadioButton tv, int id) {
        Drawable nav_up = getResources().getDrawable(id);
        nav_up.setBounds(0, 0, nav_up.getMinimumWidth(), nav_up.getMinimumHeight());
        tv.setCompoundDrawables(null, null, nav_up, null);
    }


    public void startSearch(String searchKey) {
        preSearchKey = this.searchKey;
        this.searchKey = searchKey;
        getNewData();
        if ((searchKey != null && preSearchKey != null) || !searchKey.equals(preSearchKey)) {
            specFilterPopupWindow.loadDataOnly(getSpecFilterRequestParams());
        }
        mViewModel.getSearchStartLiveData().postValue(Unit.INSTANCE);
    }

    public void setSearchKeyword(String searchKey) {
        this.searchKey = searchKey;
    }

    public void setPtByState(String isGroupBuyingOrWholesale) {
        this.isGroupBuyingOrWholesale = isGroupBuyingOrWholesale;
    }

    public void setUseJuanStatus(String isAvailableCoupons) {
        this.isAvailableCoupons = isAvailableCoupons;
    }


    @NonNull
    @Override
    public String getCouponEntryType() {
        return CouponEntryType.COUPON_ENTRY_TYPE_SEARCH_AREA_AND_SHOP;
    }
}
