package com.ybmmarket20.common.ordertopmanager

import androidx.annotation.UiThread
import com.youth.banner.util.LogUtils

/**
 * @class   OrderTopManager
 * <AUTHOR>
 * @date  2025/4/1
 * @description
 */
@UiThread
object OrderTopManager {

    const val DEFAULT_PRIORITY = -1
    private var mDialogs: ArrayList<OrderTopParam> = arrayListOf()

    @Synchronized
    private fun add(orderTopParam: OrderTopParam) {

        orderTopParam.dialog?.let {

            it.setOnShowListener {
                orderTopParam.show()
            }

            it.setOnDismissListener { isPushBack->
                orderTopParam.dismiss(isPushBack)
                if (isPushBack){
                    mDialogs.remove(orderTopParam)
                }
                showNext()
            }

            mDialogs.add(orderTopParam)
        } ?: kotlin.run {
            LogUtils.d("添加的dialog为null")
        }
    }

    @Synchronized
    fun show(orderTopParam: OrderTopParam) {
        var mIndex = -1
        mDialogs.forEachIndexed { index, params ->
            if (params.priority == orderTopParam.priority){
                mIndex = index
            }
        }
        if (mIndex != -1){
            mDialogs.removeAt(mIndex)
        }

        if (!mDialogs.contains(orderTopParam)) {
            add(orderTopParam)
        }
        orderTopParam.dialog?.let {
            if (mDialogs.size == 1 && it.isCanShow()) {
                orderTopParam.show()
            } else {
                //判断优先级  是否可以展示
                maybeShow(orderTopParam)
            }
        }

    }

    @Synchronized
    fun dismiss(orderTopParam: OrderTopParam) {
        val iterator = mDialogs.iterator()
        while (iterator.hasNext()) {
            val dialogParam = iterator.next()
            if (dialogParam == orderTopParam) {
                dialogParam.dialog?.let { mDialog ->
                    orderTopParam.dismiss(true)
                    iterator.remove()
                    showNext()
                }
                break
            }
        }

    }

    @Synchronized
    private fun maybeShow(orderTopParam: OrderTopParam) {

        orderTopParam.dialog?.let { dialog ->
            val topShowDialog: OrderTopParam? = getShowingDialog()
            val priority = orderTopParam.priority
            topShowDialog?.let {
                if (priority >= it.priority && dialog.isCanShow()) {
                    // 这里当前的优先级比显示的大 所以必然是最大的  所以直接显示  之前的被挤退
                    it.dismiss(false)
                    orderTopParam.show()
                } else {
                    //进入待显示状态
                    orderTopParam.prepare()
                }
            } ?: kotlin.run {
                //此时show的时候如果没有topShowDialog 证明没有备选的
                if (dialog.isCanShow()) {
                    orderTopParam.show()
                }
            }
        }
    }

    public fun clear() {
        mDialogs.clear()
    }

    private fun getShowingDialog(): OrderTopParam? {

        mDialogs.forEach {
            if (it.status == OrderTopStatus.SHOWING) {
                return it
            }
        }
        return null
    }

    private fun showNext() {

        getCanShowDialog()?.show()

    }

    //获取可显示的，且最大优先级的ManagerDialog
    private fun getCanShowDialog(): OrderTopParam? {
        val list: List<OrderTopParam>? = mDialogs.mapNotNull {
            //过滤只要可显示的
            it.dialog?.let { dialog ->
                if (dialog.isCanShow()) {
                    return@mapNotNull it
                }
            }
        } as? List<OrderTopParam>
        return getMaxPriorityDialog(list)
    }

    //获取最大优先级的ManagerDialog
    private fun getMaxPriorityDialog(list: List<OrderTopParam>?): OrderTopParam? {
        list?.let {
            return it.maxByOrNull { OrderTopParam ->
                OrderTopParam.priority
            }
        } ?: return null
    }
}

class OrderTopParam private constructor(
        val dialog: OrderTopManageableDialog?,
        var priority: Int,
        var status: OrderTopStatus,
) {
    class Builder {
        private var dialog: OrderTopManageableDialog? = null
        private var priority: Int = OrderTopManager.DEFAULT_PRIORITY
        private var status: OrderTopStatus = OrderTopStatus.DISMISS

        fun setDialog(dialog: OrderTopManageableDialog?): Builder {
            this.dialog = dialog
            return this
        }

        fun setPriority(priority: Int): Builder {
            this.priority = priority
            return this
        }

        fun setStatus(status: OrderTopStatus): Builder {
            this.status = status
            return this
        }


        fun build() = OrderTopParam(dialog, priority, status)
    }

    fun show() {
        dialog?.let {
            status = OrderTopStatus.SHOWING
            it.show()
        }
    }

    fun dismiss(isPushBack: Boolean) {
        dialog?.let {
            if (isPushBack) status = OrderTopStatus.DISMISS else prepare()
            it.dismiss(isPushBack)
        }
    }

    /**
     * 进入预备状态（显示过被挤退 都进入此状态）
     */
    fun prepare() {
        dialog?.let {
            status = OrderTopStatus.PREPARE_SHOW
        }
    }
}

enum class OrderTopStatus {
    SHOWING,  //展示中
    PREPARE_SHOW, //预备展示
    DISMISS  //不展示
}
