package com.ybmmarket20.bean;

/**
 * 退货列表
 */
public class RefundBean {
    public String id;//退款单id
    public String refundOrderNo;//退款单编号
    public String orderNo;//订单编号
    public String applyRefundTime;
    public String refundVarietyNum;//商品数量
    public String refundFee;
    public String auditStatusName;
    public String imageUrl;
    public int auditState;//0显示退款取消按钮
    public int refundChannel;//1:用户发起 2:客服介入
    public int audit_process_state;//0可以取消，不等于0不能取消
    public int auditProcessState;//0客服/仓库/财务 都未审核
    /*------------------渠道-------------------*/
    public String channelCode;//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱
    public int isThirdCompany;//是否是自营（0：是；1：否）
    public String cashPayAmount;
    public long toBeConfirmedCountDownTime; //待客户确认的剩余时间，单位秒。根据是否值来判断是否显示倒计时。
    public long localTime; //SystemClock.elapsedRealtime() 获取剩余时间的时间点获取该时间

    public boolean isIsThirdCompany() {
        return isThirdCompany == 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RefundBean that = (RefundBean) o;

        return id != null ? id.equals(that.id) : that.id == null;

    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
