package com.ybmmarket20.bean

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/3/25 14:39
 *    desc   :
 */
data class NextDayServiceTagListBean(
    val appIcon: String? = "",
    val bgColor: String? = "",
    val borderColor: String? = "",
    val description: String? = "",
    val name: String? = "",
    val pcIcon: String? = "",
    val sort: Int,
    val text: String? = "",
    val textColor: String? = "",
    val uiStyle: Int,
    val uiType: Int
)

data class NextDayDeliveryDtoBean(
    val tagDto: TagDtoBean,
    val tips: String? = ""

)

data class TagDtoBean(
    val appIcon: String? = "",
    val bgColor: String? = "",
    val borderColor: String? = "",
    val description: String? = "",
    val name: String? = "",
    val text: String? = "",
    val textColor: String? = "",
    val uiType: Int
)
