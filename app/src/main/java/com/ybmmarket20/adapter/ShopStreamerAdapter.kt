package com.ybmmarket20.adapter

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.StreamerItem
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.jgShopMainResourceClickTrack
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.HomeSteadyStreamerView
import com.ybmmarket20.view.homesteady.whenAllNotNull
import org.json.JSONObject

/**
 * 店铺横幅广告
 */
class ShopStreamerAdapter(data: List<StreamerItem>, val shopName: String? = "", val shopCode: String? = ""):
    YBMBaseAdapter<StreamerItem>(R.layout.item_shop_streamer, data) {

        var jgTrackBean:JgTrackBean? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: StreamerItem?) {
        whenAllNotNull(baseViewHolder, t) { holder, item ->
            val hssv_shop_streamer = holder.getView<HomeSteadyStreamerView>(R.id.hssv_shop_streamer)
            val rat = 100f/(item.hotZoneInfoList?.size?: 0)
            t?.hotZoneInfoList?.forEach {
                it.hotZoneWidth = rat.toInt()
            }
            hssv_shop_streamer.setStreamer(item)
            hssv_shop_streamer.setAnalysisCallback { action, _ ->
                val obj = JSONObject().also {
                    it.put("action", action)
                    shopName?.apply { it.put("text", shopName) }
                    shopCode?.apply { it.put("shopCode", shopCode) }
                }
                XyyIoUtil.track("shopHome_pictureCard_Click", obj)

                mContext.jgShopMainResourceClickTrack(jgTrackBean, shopId = shopCode, shopName = shopName, locationUrl = action)
            }
        }
    }
}