package com.ybmmarket20.home

import android.content.Context
import com.ybmmarket20.utils.analysis.AnalysisConst.Cart.*
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject

class CartAnalysis {

    /**
     * 购物车跨店券入口-点击事件
     */
    fun cartCrossShopCouponClick() {
        XyyIoUtil.track(ACTION_SHOPPINGCART_PLATFORMCOUPON_CLICK, getCartClickJsonObject("1"))//1-领券入口
    }

    // 跨店券引导去凑单-点击事件
    fun cartCrossShopCouponActionClick(action: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_PLATFORMCOUPONITEM_CLICK, getCartClickJsonObject("1", action = action))//1-去凑单
    }

    /**
     * 总计明细-点击事件
     */
    fun cartDetailClick() {
        XyyIoUtil.track(ACTION_SHOPPINGCART_DETAILS_CLICK, getCartClickJsonObject("1"))//1-查看明细
    }

    /**
     * 去结算-点击事件
     */
    fun cartSettleClick(name: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_SETTLEMENT_CLICK, getCartClickJsonObject(name))  // 1-领券结算 2-去结算
    }

    /**
     * 购物车店铺入口-点击事件
     */
    fun cartShopClick(shopcode: String?, action: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_SHOP_CLICK, generateShopItemJsonObject(shopcode, action))
    }

    /**
     * 购物车店铺优惠券入口-点击事件
     */
    fun cartShopCouponClick(mContext: Context, shopcode: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_COUPON_CLICK, generateShopItemJsonObject(shopcode))
        CartFragmentV3.jgBtnClickTrack(mContext,"领取优惠券")
    }


    /**
     * 收起/展开-点击事件
     */
    fun shopeItemExpandClick(shopcode: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_PACKUP_CLICK, generateShopItemJsonObject(shopcode, text = "2"))//1-收起；2-展开
    }

    /**
     * 收起/展开-点击事件
     */
    fun shopeItemCollapseClick(shopcode: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_PACKUP_CLICK, generateShopItemJsonObject(shopcode, text = "1"))//1-收起；2-展开
    }

    /**
     * 包邮注解-点击
     */
    fun shopeItemPostClick(shopcode: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_POST_CLICK, generateShopItemJsonObject(shopcode))
    }

    /**
     * 包邮去凑单-点击
     */
    fun shopeItemPostToAddClick(shopcode: String?) {
        XyyIoUtil.track(ACTION_SHOPPINGCART_POSTITEM_CLICK, generateShopItemJsonObject(shopcode))
    }

    /**
     * 满返活动去凑单-曝光
     */
    fun shopeItemReturndExposure(
        shopcode: String?,
        prom_type: String? = null,
        prom_id: String? = null,
        action: String? = null
    ) {
        XyyIoUtil.track(
            ACTION_SHOPPINGCART_RETURNITEM_EXPOSURE,
            generateShopItemJsonObject(shopcode = shopcode, prom_type = prom_type, prom_id = prom_id, action = action)
        )
    }

    /**
     * 满返活动去凑单-点击
     */
    fun shopeItemReturndClick(
        shopcode: String?,
        prom_type: String? = null,
        prom_id: String? = null,
        action: String? = null
    ) {
        XyyIoUtil.track(
            ACTION_SHOPPINGCART_RETURNITEM_CLICK,
            generateShopItemJsonObject(shopcode = shopcode, prom_type = prom_type, prom_id = prom_id, action = action)
        )
    }


    /**
     * 满类活动去凑单-曝光
     */
    fun shopeItemReductionExposure(
        shopcode: String?,
        prom_type: String? = null,
        prom_id: String? = null,
        action: String? = null
    ) {
        XyyIoUtil.track(
            ACTION_SHOPPINGCART_REDUCTIONITEM_EXPOSURE,
            generateShopItemJsonObject(shopcode = shopcode, prom_type = prom_type, prom_id = prom_id, action = action)
        )
    }


    /**
     * 满类活动去凑单-点击
     */
    fun shopeItemReductionClick(
        shopcode: String?,
        prom_type: String? = null,
        prom_id: String? = null,
        action: String? = null
    ) {
        XyyIoUtil.track(
            ACTION_SHOPPINGCART_REDUCTIONITEM_CLICK,
            generateShopItemJsonObject(shopcode = shopcode, prom_type = prom_type, prom_id = prom_id, action = action)
        )
    }


    private fun generateShopItemJsonObject(
        shopcode: String?,
        action: String? = null,
        text: String? = null,
        prom_id: String? = null,
        prom_type: String? = null
    ): JSONObject {
        return JSONObject().apply {

            put("shop_code", shopcode)
            action?.let {
                put("action", action)
            }
            text?.let {
                put("text", text)
            }
            prom_id?.let {
                put("prom_id", prom_id)
            }
            prom_id?.let {
                put("prom_type", prom_type)
            }
        }
    }

    private fun getCartClickJsonObject(name: String?, action: String? = null): JSONObject {
        return JSONObject().apply {
            put("name", name)
            action?.let {
                put("action", action)
            }
        }
    }

}