package com.ybmmarket20.activity.afterSales.activity

import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.activity.afterSales.AdapterComposeManager
import com.ybmmarket20.activity.afterSales.IComposeAdapterState
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesErrorInvoiceAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesInvoiceTypeAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesSpecialInvoiceListAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesSpecialInvoiceTitleAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesTipsAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesUploadImageAdapter
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_ERROR
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_NO
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_NORMAL
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_SPECIAL
import com.ybmmarket20.bean.aftersales.AfterSalesAcceptElectronicInvoice
import com.ybmmarket20.bean.aftersales.AfterSalesBean
import com.ybmmarket20.bean.aftersales.AfterSalesInvoiceType
import com.ybmmarket20.bean.aftersales.AfterSalesSpecialInvoice
import com.ybmmarket20.bean.aftersales.AfterSalesTips
import com.ybmmarket20.bean.aftersales.AfterSalesUploadImage
import com.ybmmarket20.bean.aftersales.RefundOrAfterSalesEvent
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.common.eventbus.EventBusUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.AfterSalesViewModel
import kotlinx.android.synthetic.main.activity_after_sales.rv
import kotlinx.android.synthetic.main.activity_after_sales.rvConfirm
import org.greenrobot.eventbus.EventBus

/**
 * 发票售后
 */
@Router("invoiceaftersalesservice")
class InvoiceAfterSalesServiceActivity : AbstractAfterSalesServiceActivity() {

    private var mOrderNo: String? = null
    private val afterSalesViewModel: AfterSalesViewModel by viewModels()

    override fun getPageTitle(): String = "发票相关售后"

    override fun initData() {
        super.initData()
        mOrderNo = intent.getStringExtra("orderNo")
        afterSalesViewModel.getInvoiceData(AFTER_SALES_INVOICE_TYPE_NORMAL, mOrderNo ?: "")
        rvConfirm.setOnClickListener {
            showProgress()
            afterSalesViewModel.submitInvoiceAfterSles(mOrderNo?: "")
        }
    }

    override fun setObserver() {
        afterSalesViewModel.afterSalesLiveData.observe(this) {
            rv.adapter = AdapterComposeManager().run {
                addWatchEdit { isSelected ->
                    rvConfirm.isActivated = isSelected
                    rvConfirm.isEnabled = isSelected
                }
                changeState {
                    getComposeAdapterState(this, it) {invoiceType->
                        afterSalesViewModel.getInvoiceData(invoiceType, mOrderNo?: "")
                    }
                }.concatAdapter()
            }
        }

        afterSalesViewModel.submitResultLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=${it.data.afterSalesNo?: ""}")
                EventBusUtil.sendEvent(Event(0, RefundOrAfterSalesEvent()))
                finish()
            }
        }
    }

    /**
     * 生成State
     */
    private fun getComposeAdapterState(
        manager: AdapterComposeManager,
        afterSalesBean: AfterSalesBean,
        changeInvoiceType: ((type: Int) -> Unit)?
    ): IComposeAdapterState {
        return when (afterSalesBean.afterSalesInvoiceType?.type) {
            //无操作和无票状态
            AFTER_SALES_INVOICE_TYPE_NORMAL,
            AFTER_SALES_INVOICE_TYPE_NO -> AfterSalesNormalInvoiceState(
                manager,
                afterSalesBean,
                changeInvoiceType
            )

            AFTER_SALES_INVOICE_TYPE_ERROR -> AfterSalesErrorInvoiceState(
                manager,
                afterSalesBean,
                changeInvoiceType
            )

            AFTER_SALES_INVOICE_TYPE_SPECIAL -> AfterSalesSpecialInvoiceState(
                manager,
                afterSalesBean,
                changeInvoiceType
            )

            else -> AfterSalesNormalInvoiceState(
                manager,
                afterSalesBean,
                changeInvoiceType
            )
        }
    }

    /**
     * 无操作状态
     */
    class AfterSalesNormalInvoiceState(
        private val afterSalesManager: AdapterComposeManager,
        private val afterSalesBean: AfterSalesBean,
        private val changeInvoiceType: ((type: Int) -> Unit)?
    ) : IComposeAdapterState {
        override fun onAddAdapter() {
            afterSalesManager
                //发票售后类型
                .addAdapter(
                    AfterSalesInvoiceTypeAdapter(
                        afterSalesBean.afterSalesInvoiceType ?: AfterSalesInvoiceType(),
                        changeInvoiceType
                    )
                )
                //补充说明
                .addAdapter(
                    AfterSalesTipsAdapter(
                        afterSalesBean.afterSalesTips ?: AfterSalesTips()
                    )
                )
                //上传凭据
                .addAdapter(
                    AfterSalesUploadImageAdapter(
                        afterSalesBean.afterSalesUploadImage ?: AfterSalesUploadImage()
                    )
                )
        }
    }

    /**
     * 错票状态
     */
    class AfterSalesErrorInvoiceState(
        private val afterSalesManager: AdapterComposeManager,
        private val afterSalesBean: AfterSalesBean,
        private val changeInvoiceType: ((type: Int) -> Unit)?
    ) : IComposeAdapterState {
        override fun onAddAdapter() {
            afterSalesManager
                //发票售后类型
                .addAdapter(
                    AfterSalesInvoiceTypeAdapter(
                        afterSalesBean.afterSalesInvoiceType ?: AfterSalesInvoiceType(),
                        changeInvoiceType
                    )
                )
                //选择发票中有误的信息
                .addAdapter(
                    AfterSalesErrorInvoiceAdapter(
                        afterSalesBean.afterSalesErrorInvoiceInfoList ?: mutableListOf()
                    )
                )
                //补充说明
                .addAdapter(
                    AfterSalesTipsAdapter(
                        afterSalesBean.afterSalesTips ?: AfterSalesTips()
                    )
                )
                //上传凭据
                .addAdapter(
                    AfterSalesUploadImageAdapter(
                        afterSalesBean.afterSalesUploadImage ?: AfterSalesUploadImage()
                    )
                )
        }
    }

    /**
     * 申请专票状态
     */
    class AfterSalesSpecialInvoiceState(
        private val afterSalesManager: AdapterComposeManager,
        private val afterSalesBean: AfterSalesBean,
        private val changeInvoiceType: ((type: Int) -> Unit)?
    ) : IComposeAdapterState {
        override fun onAddAdapter() {
            afterSalesManager
                //发票售后类型
                .addAdapter(
                    AfterSalesInvoiceTypeAdapter(
                        afterSalesBean.afterSalesInvoiceType ?: AfterSalesInvoiceType(),
                        changeInvoiceType
                    )
                )
                //核实专票信息
                .addAdapter(
                    AfterSalesSpecialInvoiceTitleAdapter(
                        afterSalesBean.afterSalesAcceptElectronicInvoice
                            ?: AfterSalesAcceptElectronicInvoice()
                    )
                )
                //核实专票信息列表
                .addAdapter(
                    AfterSalesSpecialInvoiceListAdapter(
                        afterSalesBean.afterSalesSpecialInvoiceList
                            ?: AfterSalesSpecialInvoice().toList()
                    )
                )
                //补充说明
                .addAdapter(
                    AfterSalesTipsAdapter(
                        afterSalesBean.afterSalesTips ?: AfterSalesTips()
                    )
                )
                //上传凭据
                .addAdapter(
                    AfterSalesUploadImageAdapter(
                        afterSalesBean.afterSalesUploadImage ?: AfterSalesUploadImage()
                    )
                )
        }
    }

}