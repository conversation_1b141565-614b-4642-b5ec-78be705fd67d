package com.ybmmarket20.activity

import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.AuthenticationProcessingInfo
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.AuthenticationProcessingViewModel
import kotlinx.android.synthetic.main.activity_associated_shop_authentication_processing.*
import kotlinx.android.synthetic.main.common_header_items.*

/**
 * 关联店铺审核中
 */
@Router("associatedshopauthenticationprocessing")
open class AssociatedShopAuthenticationProcessingActivity: BaseActivity() {

    val mViewModel: AuthenticationProcessingViewModel by viewModels()
    private var merchantId = ""
    private val from: Int = 0

    override fun getContentViewId(): Int = R.layout.activity_associated_shop_authentication_processing

    override fun initData() {
        setTitle("资质审核中")
        val status = intent.getStringExtra("status")?.toInt()
         if (status == 2) {
             tvQuiteSingle.visibility = View.GONE
             tvQuite.visibility = View.VISIBLE
             tvOtherShop.visibility = View.VISIBLE
        } else {
             tvQuiteSingle.visibility = View.VISIBLE
             tvQuite.visibility = View.GONE
             tvOtherShop.visibility = View.GONE
         }
        merchantId = intent.getStringExtra("merchantId") ?: ""
        iv_back.visibility = View.GONE
        initObserver()
        initListener()
        showProgress()
        mViewModel.queryAuthenticationProcessingInfo(SpUtil.getAccountId(), merchantId?: "")
        tvQuiteSingle.setOnClickListener {
            if (from == 0) {
                RoutersUtils.open("ybmpage://login")
            } else {
//                RoutersUtils.open("ybmpage://accountbasicinfo")
                gotoAccountBasicInfo()
            }
            quiteBtnClick()
        }
    }

    private fun initObserver() {
        mViewModel.authenticationProcessingInfoLiveData.observe(this, Observer {
            dismissProgress()
            if (it.isSuccess) {
                val info = it.data
                etShopName.setText(info.name)
                etAddress.setText("${info.province}${info.city}${info.district}${info.street?: ""}")
                etAddressDetail.setText(info.address)
                setData(info)
            }
        })
    }

    open fun setData(info: AuthenticationProcessingInfo) {

    }

    private fun initListener() {
        tvQuite.setOnClickListener {
            if (from == 0) {
                RoutersUtils.open("ybmpage://login")
            } else {
//                RoutersUtils.open("ybmpage://accountbasicinfo")
                gotoAccountBasicInfo()
            }
            quiteBtnClick()
        }

        tvOtherShop.setOnClickListener {
            RoutersUtils.open("ybmpage://selectloginshop")
            selectOtherShop()
        }
    }

    private fun gotoAccountBasicInfo() {
        startActivity(Intent(this, AccountBasicInfoActivity::class.java))
    }

    open fun quiteBtnClick() {
        XyyIoUtil.trackForAccount("action_associationAudit_logout_click")
    }

    open fun selectOtherShop() {
        XyyIoUtil.trackForAccount("action_associationAudit_selectPoi_click")
    }

    override fun onBackPressed() {
        super.onBackPressed()
        if (from == 0) {
            RoutersUtils.open("ybmpage://login")
        }
    }

}