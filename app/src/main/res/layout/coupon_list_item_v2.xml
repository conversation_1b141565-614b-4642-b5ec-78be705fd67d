<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/ll_left"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="8dp"
        android:layout_weight="2"
        android:background="@drawable/bg_coupon_left"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_price_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="bottom"
                android:paddingBottom="8dp"
                android:text="¥"
                android:textColor="@color/white"
                android:textSize="@dimen/couponmember_text04" />

            <TextView
                android:id="@+id/tv_coupon_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="bottom"
                android:text="100"
                android:textColor="@color/white"
                android:textSize="@dimen/couponmember_new" />

            <TextView
                android:id="@+id/tv_discount_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="bottom"
                android:paddingBottom="8dp"
                android:text="折"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:visibility="gone" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_coupon_condition"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:gravity="center"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/couponmember_text02"
            tools:text="满200元使用" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/ll_right"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginRight="8dp"
        android:layout_weight="5"
        android:background="@drawable/bg_coupon_right"
        android:padding="10dp">

        <com.ybmmarket20.view.SurroundTagView
            android:id="@+id/tv_tag_and_condition"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.1"
            android:maxLines="2"
            android:textColor="#FF6F7A87"
            android:textSize="14sp"
            app:tag_background="#ffcccccc"
            app:tag_color="#ffffffff" />

        <TextView
            android:id="@+id/tv_coupon_use_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="5dp"
            android:textColor="#FFcccccc"
            android:textSize="@dimen/couponmember_text03"
            tools:text="2016.1.1-2016-12-30" />


        <TextView
            android:id="@+id/tv_coupon_max"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_above="@id/tv_coupon_use_time"
            android:layout_marginBottom="5dp"
            android:background="@drawable/bg_max_coupon"
            android:gravity="center_vertical"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textColor="#F04134"
            android:textSize="13sp"
            tools:text="最高优惠120" />

        <TextView
            android:id="@+id/tv_start_use"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:background="@drawable/bg_rect_radio_purple_border"
            android:text="立即使用"
            android:textColor="@color/color_ff4244"
            android:textSize="14sp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_coupon_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:src="@drawable/icon_used" />
    </RelativeLayout>

</LinearLayout>