<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="16dp">

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginStart="16dp"
        android:src="@drawable/icon_gift_no_select"
        app:layout_constraintBottom_toBottomOf="@id/iv_gift"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_gift" />

    <ImageView
        android:id="@+id/iv_gift"
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:layout_marginStart="1dp"
        app:layout_constraintStart_toEndOf="@id/iv_select"
        app:layout_constraintTop_toTopOf="parent" />
    
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_no_inventory"
        android:text="无库存"
        android:visibility="gone"
        tools:visibility="visible"
        app:rv_backgroundColor="@color/color_B3000000"
        app:rv_cornerRadius="22dp"
        android:textSize="12dp"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/iv_gift"
        app:layout_constraintEnd_toEndOf="@id/iv_gift"
        app:layout_constraintTop_toTopOf="@id/iv_gift"
        app:layout_constraintBottom_toBottomOf="@id/iv_gift"
        android:gravity="center"
        android:layout_width="44dp"
        android:layout_height="44dp"/>
    

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="10dp"
        android:textColor="@color/color_292933"
        android:textSize="15dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="京都 念慈庵川贝枇杷膏枇杷500ml/瓶500ml/瓶"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_gift"
        app:layout_constraintTop_toTopOf="@id/iv_gift" />

    <TextView
        android:id="@+id/tv_validity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="13dp"
        android:textColor="@color/text_color_666666"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginTop="6dp"
        tools:text="效期：2021-10-12" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        app:layout_constraintStart_toStartOf="@id/tv_validity"
        app:layout_constraintTop_toBottomOf="@id/tv_validity"
        android:layout_marginTop="6dp"
        tools:text="¥0.01"
        android:textStyle="bold"
        android:textColor="@color/color_ff2121"
        android:textSize="16dp"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_unit"
        tools:text="/瓶"
        android:textSize="11dp"
        android:textColor="@color/text_color_333333"
        android:layout_width="wrap_content"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_price"
        app:layout_constraintStart_toEndOf="@id/tv_price"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/iv_sigh"
        android:src="@drawable/icon_gift_sigh"
        app:layout_constraintTop_toTopOf="@id/tv_unit"
        app:layout_constraintBottom_toBottomOf="@id/tv_unit"
        app:layout_constraintStart_toEndOf="@id/tv_unit"
        android:layout_marginStart="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_origin_price"
        app:layout_constraintStart_toEndOf="@id/iv_sigh"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_unit"
        android:layout_width="wrap_content"
        tools:text="¥15.00"
        android:textColor="@color/text_color_999999"
        android:text="12dp"
        android:layout_marginStart="7dp"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_limit"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="14dp"
        app:layout_constraintBottom_toTopOf="@id/cl_number"
        android:layout_marginBottom="3dp"
        tools:text="5件起送，限20件"
        android:textColor="@color/text_color_666666"
        android:gravity="center"
        android:minWidth="76dp"
        android:textSize="10dp"
        app:layout_constraintStart_toEndOf="@id/tv_validity"
        app:layout_constraintHorizontal_bias="1"
        android:maxLines="1"
        android:layout_marginStart="5dp"
        android:ellipsize="end"
        app:layout_constrainedWidth="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/cl_number"
        android:layout_width="76dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginTop="19dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="16dp"
        app:rv_backgroundColor="@color/white"
        app:rv_strokeColor="@color/color_E6E6E6"
        app:rv_strokeWidth="@dimen/dimen_dp_1"
        app:rv_cornerRadius="@dimen/dimen_dp_13"
        android:layout_height="26dp">

        <ImageView
            android:id="@+id/iv_minus"
            android:layout_width="26dp"
            android:src="@drawable/icon_gift_minus"
            android:padding="7dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_height="26dp"/>

        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="26dp"
            android:src="@drawable/icon_gift_add"
            android:padding="7dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="26dp"/>
        
        <TextView
            android:id="@+id/tv_number"
            android:layout_width="wrap_content"
            android:text="1"
            android:textColor="@color/color_292933"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textSize="13dp"
            android:textStyle="bold"
            android:layout_height="wrap_content"/>

    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="@id/cl_number"
        android:background="@color/colors_eeeeee"
        app:layout_constraintTop_toBottomOf="@id/tv_price"
        android:layout_marginTop="14dp"
        android:layout_height="1dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>