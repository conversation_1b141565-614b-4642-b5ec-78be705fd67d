<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_32">

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginStart="@dimen/dimen_dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/logo" />

    <TextView
        android:id="@+id/tvBankName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_39"
        android:text="建设银行储蓄卡(2234)"
        android:textColor="#292933"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RadioButton
        android:id="@+id/rbCheck"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:background="@drawable/payway_bg_selector"
        android:checked="false"
        android:button="@null"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/clickView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</androidx.constraintlayout.widget.ConstraintLayout>