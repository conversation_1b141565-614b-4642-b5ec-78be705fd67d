# 店铺内搜索极光埋点移除测试文档

## 修改概述

本次修改移除了店铺内搜索页面的以下4个极光埋点事件：
- `page_list_product_exposure` - 商品列表曝光事件
- `page_list_build` - 列表生成事件  
- `action_list_product_click` - 商品列表点击事件
- `action_product_button_click` - 商品按钮点击事件

**重要说明**：此修改只影响店铺内搜索，不会影响大搜页面的极光埋点功能。

## 修改文件列表

### 1. ShopGoodsFragment.java
**文件路径**: `app/src/main/java/com/ybmmarket20/business/shop/ui/ShopGoodsFragment.java`

**修改内容**:
- 注释了 `resourceViewTrackListener` 回调设置（第190-235行）
- 注释了 `productClickTrackListener` 回调设置（第236-276行）

**影响的埋点事件**: `page_list_product_exposure`, `action_list_product_click`, `action_product_button_click`

### 2. GoodListAdapterNew.kt  
**文件路径**: `app/src/main/java/com/ybmmarketkotlin/adapter/GoodListAdapterNew.kt`

**修改内容**:
- 在极光埋点调用处添加店铺搜索判断条件（第287-294行）
- 在商品点击回调处添加店铺搜索判断条件（第281-285行）

**判断逻辑**: 通过 `jgTrackBean?.pageId == JGTrackManager.TrackShopSearch.PAGE_ID` 识别店铺搜索场景

**影响的埋点事件**: `page_list_product_exposure`, `action_list_product_click`, `action_product_button_click`

### 3. ShopGoodsTabFragment.kt
**文件路径**: `app/src/main/java/com/ybmmarket20/business/shop/ui/ShopGoodsTabFragment.kt`

**修改内容**:
- 注释了 `resourceViewTrackListener` 回调设置（第114-139行）
- 注释了 `productClickTrackListener` 回调设置（第141-165行）

**影响的埋点事件**: `page_list_product_exposure`, `action_list_product_click`, `action_product_button_click`

## 测试范围

### 需要测试的页面/功能

#### 1. 店铺内搜索页面
**页面路径**: 店铺主页 → 搜索框 → 输入关键词搜索
**测试步骤**:
1. 进入任意店铺主页
2. 点击搜索框，输入商品关键词
3. 点击搜索按钮，进入店铺搜索结果页
4. 浏览商品列表，点击商品卡片
5. 点击商品上的按钮（如加购物车按钮）

**预期结果**: 
- 不应触发 `page_list_product_exposure` 事件
- 不应触发 `action_list_product_click` 事件  
- 不应触发 `action_product_button_click` 事件
- 其他功能正常（商品详情页跳转、加购物车等）

#### 2. 店铺商品Tab页面
**页面路径**: 店铺主页 → 全部商品Tab
**测试步骤**:
1. 进入任意店铺主页
2. 点击"全部商品"Tab
3. 浏览商品列表，点击商品卡片
4. 点击商品上的按钮

**预期结果**: 同店铺内搜索页面

### 需要验证不受影响的页面

#### 1. 大搜页面（重要）
**页面路径**: 首页 → 搜索框 → 输入关键词搜索
**测试步骤**:
1. 在首页搜索框输入关键词
2. 点击搜索，进入大搜结果页
3. 浏览商品列表，点击商品卡片
4. 点击商品上的按钮

**预期结果**: 
- 应正常触发 `page_list_product_exposure` 事件
- 应正常触发 `page_list_build` 事件
- 应正常触发 `action_list_product_click` 事件
- 应正常触发 `action_product_button_click` 事件

#### 2. 其他搜索页面
- 专区搜索页面
- 常购常搜页面
- 其他使用 SearchAdapter 的页面

## 技术实现说明

### 区分店铺搜索和大搜的方法

1. **页面ID判断**: 通过 `JGTrackManager.TrackShopSearch.PAGE_ID` 识别店铺搜索
2. **适配器类型**: 
   - 店铺搜索使用 `GoodListAdapterNew`
   - 大搜使用 `SearchAdapter`
3. **请求参数**: 店铺搜索请求中 `spFrom="2"`

### 修改策略

1. **注释法**: 在店铺搜索专用的Fragment中直接注释极光埋点回调
2. **条件判断法**: 在共用的Adapter中添加条件判断，避免影响其他页面

## 风险评估

### 低风险
- 店铺搜索功能本身不受影响
- 大搜页面的极光埋点不受影响
- 其他埋点系统（雪地埋点、QT埋点）不受影响

### 需要关注
- 确保条件判断逻辑正确，避免误伤其他页面
- 验证所有店铺搜索相关页面都已覆盖

## 验证方法

1. **代码审查**: 确认修改逻辑正确
2. **功能测试**: 验证店铺搜索功能正常
3. **埋点验证**: 确认极光埋点事件不再触发
4. **回归测试**: 验证大搜等其他页面不受影响
